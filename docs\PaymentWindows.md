# Payment Windows with Default Configuration

## Overview

The PaymentWindowService now supports a **default configuration pattern** where:
- **Default Config**: Always active configuration that handles regular payment methods processing
- **Payment Windows**: Specific time-based windows that temporarily override the default behavior

This approach ensures continuous payment processing with special rules during designated time periods.

## Key Concepts

### Default Configuration
- **Always Active**: Processes payments when no specific payment windows are active
- **Baseline Rules**: Defines allowed payment methods and payment types

### Payment Windows
- **Temporary Override**: Takes precedence over default config when active
- **Specific Rules**: Can restrict or allow different payment methods and payment types during specific times
- **Priority-Based**: Multiple windows can be active simultaneously with priority handling ??? - disabled for now

## Architecture

```
┌───────────────────────────────────────────────────────────────┐
│                    Payment Processing Time                    │
├───────────────────────────────────────────────────────────────┤
│ 00:00 ─────────── 09:00 ────── 09:30 ─────── 15:00 ── 16:00 ──│
│   ↓                  ↓          ↓            ↓        ↓       │
│ Default           Window1    Default      Window2  Default    │
│ Config             Active     Config       Active   Config    │
│                                                               │
│ • All methods  • SameDay only • All methods  • Ach    • All   │
└───────────────────────────────────────────────────────────────┘
```

## Key Features

### 1. Multiple Payment Windows
- Configure 2 or more independent payment windows
- Each window can have different schedules, durations, and allowed transaction types
- Windows can overlap or be completely separate

### 2. Window Priority System
- Each window has a priority level (lower number = higher priority)
- During overlapping windows, transactions are prioritized by window priority
- Prefunded payments always have the highest priority regardless of window

### 3. Transaction Type Filtering
- Each window can specify which transaction types are allowed
- Common configurations:
  - ACH Push only during high-priority windows
  - All ACH types during standard windows
  - Internal transfers only during weekend windows

### 4. Flexible Scheduling
- Per-window active days configuration
- Different start times and durations for each window
- Support for weekday, weekend, or mixed schedules

## Configuration Examples
- It is stored at appsetitngs.json under `PaymentWindowConfig` section.

```