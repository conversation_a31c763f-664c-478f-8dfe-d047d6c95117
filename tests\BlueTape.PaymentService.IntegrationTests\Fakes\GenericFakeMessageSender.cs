﻿using BlueTape.ServiceBusMessaging.Attributes;
using System.Collections.Concurrent;

namespace BlueTape.PaymentService.IntegrationTests.Fakes;
public class FakePaymentFlowServiceMessageSender<T>
{
    private readonly ConcurrentQueue<T> _messages = new ConcurrentQueue<T>();

    public Task SendMessage(ServiceBusMessageBt<T> messageBt, CancellationToken ct = default)
    {
        _messages.Enqueue(messageBt.MessageBody);
        return Task.CompletedTask;
    }

    public Task SendMessages(IEnumerable<ServiceBusMessageBt<T>> messages, CancellationToken ct = default)
    {
        foreach (var message in messages)
        {
            _messages.Enqueue(message.MessageBody);
        }
        return Task.CompletedTask;
    }

    public bool TryDequeue(out T message)
    {
        return _messages.TryDequeue(out message);
    }

    public IEnumerable<T> DequeueAll()
    {
        while (_messages.TryDequeue(out var message))
        {
            yield return message;
        }
    }

    public int Count => _messages.Count;

    public IEnumerable<T> GetAll()
    {
        return _messages.ToArray();
    }

    public bool TryPeek(out T message)
    {
        return _messages.TryPeek(out message);
    }

    public void Clear()
    {
        while (_messages.TryDequeue(out _))
        {
            // Keep dequeuing until empty
        }
    }
}