using Azure.Data.Tables;
using BlueTape.AionServiceClient.Abstractions;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.EmailSender.Abstractions;
using BlueTape.Integrations.Aion.AzureTableStorage.Abstractions;
using BlueTape.Integrations.Aion.AzureTableStorage.Entities;
using BlueTape.Integrations.Aion.Infrastructure.Constants;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.LinqpalClient.Abstractions;
using BlueTape.LoanServiceClient.Abstractions.HttpClients;
using BlueTape.LoanServiceClient.Abstractions.Services;
using BlueTape.Notification.Sender.Abstractions;
using BlueTape.PaymentService.API.Constants;
using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.API.ViewModels.Base;
using BlueTape.PaymentService.API.ViewModels.Company;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.FinalPayment;
using BlueTape.PaymentService.DataAccess.Contexts;
using BlueTape.PaymentService.DataAccess.External.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringFinalPayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FinalPayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoiceDisbursementV2;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePaymentV2;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.SubscriptionFeePayment;
using BlueTape.PaymentService.Domain.Models;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.IntegrationTests.Constants;
using BlueTape.PaymentService.IntegrationTests.Extensions;
using BlueTape.PaymentService.IntegrationTests.Fakes;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.Reporting.Application.Abstractions.LMS;
using BlueTape.SNS.SlackNotification;
using BlueTape.Utilities.Constants;
using BlueTape.Utilities.Providers;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SendGrid;
using System.Text;
using System.Text.Json;

namespace BlueTape.PaymentService.IntegrationTests.PaymentFlowTests.Base;

public class BasePaymentFlowTest : IDisposable
{
    protected readonly HttpClient _httpClient;
    private readonly DatabaseContext? _dbContext;
    private readonly IUnitOfWork _unitOfWork;
    private readonly WebApplicationFactory<Program> _webApplicationFactory;
    private readonly ITransactionNumberService _transactionNumberService;
    protected readonly FakePaymentFlowServiceMessageSender _fakePaymentFlowServiceMessageSender;
    private readonly FakeTransactionStatusMessageSender _fakeTransactionStatusMessageSender;
    private readonly FakeNotificationMessageSender _fakeNotificationMessageSender;
    private readonly FakeAzureRepository _fakeAzureRepository;
    protected readonly IDateProvider _dateProviderMock = Substitute.For<IDateProvider>();
    protected readonly IServiceProvider _serviceProvider;

    public BasePaymentFlowTest(
        bool usePostgre = false,
        bool useValidation = false,
        string dbName = "myshareddb",
        bool noEnoughBalance = false,
        bool mockDateProvider = false)
    {
        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, EnvironmentConstants.IntegrationTest);

        _webApplicationFactory = new WebApplicationFactory<Program>().WithWebHostBuilder(builder =>
        {
            builder.ConfigureTestServices(services =>
            {
                if (!usePostgre)
                {
                    services.RemoveAll<DbContextOptions<DatabaseContext>>();

                    var connectionString = $"DataSource={dbName};mode=memory;cache=shared";
                    var keepAliveConnection = new SqliteConnection(connectionString);
                    keepAliveConnection.Open();

                    services.AddDbContext<DatabaseContext>(options =>
                    {
                        // Use SQLite there because inmemory not fully supports GroupBy queries
                        options.UseSqlite(keepAliveConnection);
                    });
                }

                services.RemoveAll<IPaymentFlowServiceMessageSender>();
                services.AddSingleton<IPaymentFlowServiceMessageSender, FakePaymentFlowServiceMessageSender>();

                services.RemoveAll<INotificationMessageSender>();
                services.AddSingleton<INotificationMessageSender, FakeNotificationMessageSender>();

                services.RemoveAll<ILedgerPaymentMessageSender>();
                services.AddScoped(_ => MockedServices.MockLedgerPaymentMessageSender());

                services.RemoveAll<IOperationSyncMessageSender>();
                services.AddScoped(_ => MockedServices.MockOperationSyncMessageSender());

                var aionExternalService = services.SingleOrDefault(d =>
                    d.ServiceType == typeof(IAionHttpClient));
                if (aionExternalService != null)
                {
                    services.Remove(aionExternalService);
                    var mockedAionService = MockedServices.MockAionHttpClient(noEnoughBalance);
                    services.AddScoped(_ => mockedAionService);
                }

                var invoiceExternalService = services.SingleOrDefault(d =>
                    d.ServiceType == typeof(IInvoiceHttpClient));
                if (invoiceExternalService != null)
                {
                    services.Remove(invoiceExternalService);
                    var mockedInvoiceService = MockedServices.MockInvoiceHttpClient();
                    services.AddScoped(_ => mockedInvoiceService);
                }

                var nodeJsExternalService = services.SingleOrDefault(d =>
                    d.ServiceType == typeof(ILinqpalHttpClient));
                if (nodeJsExternalService != null)
                {
                    services.Remove(nodeJsExternalService);
                    var mockedNodeJsService = MockedServices.MockNodeJsHttpClient();
                    services.AddScoped(_ => mockedNodeJsService);
                }

                services.RemoveAll<IKeyVaultService>();
                services.AddScoped(_ => MockedServices.MockKeyVaultService());

                services.RemoveAll<IConfiguration>();
                services.AddTransient(_ => MockedServices.MockConfiguration());

                services.RemoveAll<ISnsEndpoint>();
                services.AddScoped(_ => MockedServices.MockSnsEndpoint());

                services.RemoveAll<IPaymentCompatibilityService>();
                services.AddSingleton(_ => MockedServices.MockPaymentCompatibilityService());

                services.RemoveAll<ICustomerRepository>();
                services.AddScoped(_ => MockedServices.MockCustomerRepository());

                services.RemoveAll<ISequencesRepository>();
                services.AddScoped(_ => MockedServices.MockSequencesRepository());

                services.RemoveAll<IUserRepository>();
                services.AddScoped(_ => MockedServices.MockUserRepository());

                services.RemoveAll<IUserRoleRepository>();
                services.AddScoped(_ => MockedServices.MockUserRoleRepository());

                services.RemoveAll<ICompanyHttpClient>();
                services.AddScoped(_ => MockedServices.MockCompanyHttpClient());

                services.RemoveAll<ILoanExternalService>();
                services.AddScoped(_ => MockedServices.MockLoanHttpExternalService());

                services.RemoveAll<IOperationsService>();
                services.AddScoped(_ => MockedServices.MockDatabaseOperationsService());

                services.RemoveAll<IConnectorMessageSender>();
                services.AddScoped(_ => MockedServices.MockConnectorMessageSender());

                services.RemoveAll<IInvoiceRepository>();
                services.AddScoped(_ => MockedServices.MockInvoiceRepository());

                services.RemoveAll<IOperationsRepository>();
                services.AddScoped(_ => MockedServices.MockOperationRepository());

                services.AddDbContext<TestAzureStorageDbContext>(options =>
                {
                    options.UseInMemoryDatabase("AzureStorage");
                });

                services.RemoveAll<IAzureStorageTransactionRepository>();
                services.AddScoped<IAzureStorageTransactionRepository, FakeAzureRepository>();

                services.RemoveAll<ITransactionStatusMessageSender>();
                services.AddSingleton<ITransactionStatusMessageSender, FakeTransactionStatusMessageSender>();

                services.RemoveAll<IFinalPaymentOperationSyncService>();
                services.AddScoped(_ => MockedServices.MockOperationSyncService());

                services.RemoveAll<IDrawApprovalRepository>();
                services.AddScoped(_ => MockedServices.MockDrawApprovalRepository());

                services.RemoveAll<ILoanExternalService>();
                services.AddScoped(_ => MockedServices.MockLoanExternalService());

                services.RemoveAll<ISendGridClient>();
                services.AddScoped(_ => MockedServices.MockSendGridClient());

                services.RemoveAll<ISendGridBtClient>();
                services.AddScoped(_ => MockedServices.MockSendGridBtClient());

                services.RemoveAll<INotificationReceiversService>();
                services.AddScoped(_ => MockedServices.MockNotificationReceiversService());

                services.RemoveAll<INotificationsRepository>();
                services.AddScoped(_ => MockedServices.MockNotificationsRepository());

                services.RemoveAll<ILoanReportingService>();
                services.AddScoped(_ => MockedServices.MockLoanReportingService());

                services.RemoveAll<ILoanApplicationRepository>();
                services.AddScoped(_ => MockedServices.MockLoanApplicationRepository());

                services.RemoveAll<ILoanServiceHttpClient>();
                services.AddScoped(_ => MockedServices.MockLoanServiceHttpClient());

                services.RemoveAll<ILmsExternalService>();
                services.AddScoped(_ => MockedServices.MockLmsExternalService());

                services.RemoveAll<ILoanManagementService>();
                services.AddScoped(_ => MockedServices.MockLoanManagementServiceService());

                services.RemoveAll<ICreditApplicationAuthorizationDetailsRepository>();
                services.AddScoped(_ => MockedServices.MockCreditApplicationAuthorizationDetailsRepository());

                services.RemoveAll<ILoanPricingPackageRepository>();
                services.AddScoped(_ => MockedServices.MockLoanPricingPackageRepository());

                services.RemoveAll<INotificationSender>();
                services.AddScoped(_ => MockedServices.MockNotificationSender());

                // Add IDateProvider mock to DI container
                if (mockDateProvider)
                {
                    services.RemoveAll<IDateProvider>();
                    services.AddScoped(_ => _dateProviderMock);
                }

                if (!useValidation)
                {
                    services.RemoveAll<IPaymentRequestValidator>();
                    services.AddScoped(_ => MockedServices.MockPaymentRequestValidator());
                }
            });
        });

        _httpClient = _webApplicationFactory.CreateClient();
        _httpClient.DefaultRequestHeaders.Add(ConfigurationKeys.ApiKeyName, ConfigurationKeys.ApiKeyName);
        _httpClient.Timeout = TimeSpan.FromMinutes(10);

        _serviceProvider = _webApplicationFactory.Services.CreateScope().ServiceProvider;

        _dbContext = _serviceProvider.GetService<DatabaseContext>()!;
        _dbContext?.Database.EnsureCreated();

        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });

        _unitOfWork = _serviceProvider.GetService<IUnitOfWork>()!;
        _transactionNumberService = _serviceProvider.GetService<ITransactionNumberService>()!;

        _fakePaymentFlowServiceMessageSender = (FakePaymentFlowServiceMessageSender)_serviceProvider.GetService<IPaymentFlowServiceMessageSender>()!;
        _fakeTransactionStatusMessageSender = (FakeTransactionStatusMessageSender)_serviceProvider.GetService<ITransactionStatusMessageSender>()!;
        _fakeNotificationMessageSender = (FakeNotificationMessageSender)_serviceProvider.GetService<INotificationMessageSender>()!;

        _fakeAzureRepository = (FakeAzureRepository)_serviceProvider.GetService<IAzureStorageTransactionRepository>()!;
    }

    protected async Task MakePaymentRequestProcessing(Guid id, int processingTransactionSequence)
    {
        var paymentRequest = await _unitOfWork.GetById<PaymentRequestEntity>(id, CancellationToken.None,
            $"{nameof(PaymentRequestEntity.PaymentRequestCommands)},{nameof(PaymentRequestEntity.Transactions)}");
        var commands = await _unitOfWork.GetCommandsByPaymentRequestId(id, CancellationToken.None);
        var transactions = commands.Select(x => x.Transaction);

        paymentRequest!.Status = PaymentRequestStatus.Processing;

        foreach (var transaction in transactions)
        {
            var newStatus = transaction!.SequenceNumber >= processingTransactionSequence
                ? TransactionStatus.Placed
                : TransactionStatus.Cleared;
            newStatus = transaction.SequenceNumber == processingTransactionSequence
                ? TransactionStatus.Processing
                : newStatus;

            transaction!.Status = newStatus;

            if (newStatus == TransactionStatus.Processing)
                transaction.TransactionNumber = await _transactionNumberService.GetUniqueTransactionNumber(CancellationToken.None);
        }

        foreach (var command in commands)
        {
            if (command.Transaction!.Status == TransactionStatus.Placed)
                command.Status = CommandStatus.Placed;

            if (command.Transaction.Status == TransactionStatus.Cleared)
                command.Status = CommandStatus.Executed;

            if (command.Transaction.Status == TransactionStatus.Processing)
                command.Status = CommandStatus.Executing;
        }

        await _unitOfWork.PaymentRequestCommandRepository.UpdateRange(commands, CancellationToken.None);
        await _unitOfWork.PaymentTransactionRepository.UpdateRange(transactions.ToList()!, CancellationToken.None);
        await _unitOfWork.PaymentRequestRepository.Update(paymentRequest, CancellationToken.None);
        await _unitOfWork.SaveAsync(CancellationToken.None);
    }

    protected async Task MakePaymentRequestPending(Guid id, int pendingCommandSequence)
    {
        var paymentRequest = await _unitOfWork.GetById<PaymentRequestEntity>(id, CancellationToken.None,
            $"{nameof(PaymentRequestEntity.PaymentRequestCommands)},{nameof(PaymentRequestEntity.Transactions)}");
        var commands = await _unitOfWork.GetCommandsByPaymentRequestId(id, CancellationToken.None);
        var transactions = commands.Select(x => x.Transaction);

        paymentRequest!.Status = PaymentRequestStatus.Processing;

        foreach (var transaction in transactions)
        {
            var newStatus = transaction!.SequenceNumber >= pendingCommandSequence
                ? TransactionStatus.Placed
                : TransactionStatus.Cleared;
            transaction!.Status = newStatus;

            if (newStatus == TransactionStatus.Cleared)
                transaction.TransactionNumber = await _transactionNumberService.GetUniqueTransactionNumber(CancellationToken.None);
        }

        foreach (var command in commands)
        {
            if (command.Transaction!.Status == TransactionStatus.Placed)
                command.Status = CommandStatus.Placed;

            if (command.Transaction.Status == TransactionStatus.Cleared)
                command.Status = CommandStatus.Executed;

            if (command.Transaction.SequenceNumber == pendingCommandSequence)
                command.Status = CommandStatus.Pending;
        }

        await _unitOfWork.PaymentRequestCommandRepository.UpdateRange(commands, CancellationToken.None);
        await _unitOfWork.PaymentTransactionRepository.UpdateRange(transactions.ToList()!, CancellationToken.None);
        await _unitOfWork.PaymentRequestRepository.Update(paymentRequest, CancellationToken.None);
        await _unitOfWork.SaveAsync(CancellationToken.None);
    }

    protected async Task MakePaymentRequestSettled(Guid id)
    {
        var paymentRequest = await _unitOfWork.GetById<PaymentRequestEntity>(id, CancellationToken.None,
            $"{nameof(PaymentRequestEntity.PaymentRequestCommands)},{nameof(PaymentRequestEntity.Transactions)}");

        paymentRequest!.Status = PaymentRequestStatus.Settled;
        foreach (var command in paymentRequest.PaymentRequestCommands)
        {
            command.Status = CommandStatus.Executed;
        }

        foreach (var transaction in paymentRequest.Transactions)
        {
            transaction!.Status = TransactionStatus.Cleared;
            transaction.TransactionNumber = await _transactionNumberService.GetUniqueTransactionNumber(CancellationToken.None);
        }

        await _unitOfWork.PaymentRequestCommandRepository.UpdateRange(paymentRequest.PaymentRequestCommands.ToList(), CancellationToken.None);
        await _unitOfWork.PaymentTransactionRepository.UpdateRange(paymentRequest.Transactions.ToList(), CancellationToken.None);
        await _unitOfWork.PaymentRequestRepository.Update(paymentRequest, CancellationToken.None);
        await _unitOfWork.SaveAsync(CancellationToken.None);
    }

    protected async Task MakePaymentRequestAborted(Guid id)
    {
        var paymentRequest = await _unitOfWork.GetById<PaymentRequestEntity>(id, CancellationToken.None,
            $"{nameof(PaymentRequestEntity.PaymentRequestCommands)},{nameof(PaymentRequestEntity.Transactions)}");

        paymentRequest!.Status = PaymentRequestStatus.Aborted;
        foreach (var command in paymentRequest.PaymentRequestCommands)
        {
            command.Status = CommandStatus.Aborted;
        }

        foreach (var transaction in paymentRequest.Transactions)
        {
            transaction!.Status = TransactionStatus.Aborted;
        }

        await _unitOfWork.PaymentRequestCommandRepository.UpdateRange(paymentRequest.PaymentRequestCommands, CancellationToken.None);
        await _unitOfWork.PaymentTransactionRepository.UpdateRange(paymentRequest.Transactions.ToList(), CancellationToken.None);
        await _unitOfWork.PaymentRequestRepository.Update(paymentRequest, CancellationToken.None);
        await _unitOfWork.SaveAsync(CancellationToken.None);
    }

    protected async Task MakePaymentRequestFailed(Guid id, int failedTransactionSequence)
    {
        var paymentRequest = await _unitOfWork.GetById<PaymentRequestEntity>(id, CancellationToken.None);
        var commands = await _unitOfWork.GetCommandsByPaymentRequestId(id, CancellationToken.None);
        var transactions = commands.Select(x => x.Transaction);

        paymentRequest!.Status = PaymentRequestStatus.Failed;

        foreach (var transaction in transactions)
        {
            var newStatus = transaction!.SequenceNumber > failedTransactionSequence
                ? TransactionStatus.Aborted
                : TransactionStatus.Cleared;
            newStatus = transaction.SequenceNumber == failedTransactionSequence ? TransactionStatus.Error : newStatus;

            transaction!.Status = newStatus;

            if (newStatus == TransactionStatus.Cleared)
                transaction.TransactionNumber = await _transactionNumberService.GetUniqueTransactionNumber(CancellationToken.None);
        }

        foreach (var command in commands)
        {
            if (command.Transaction!.Status == TransactionStatus.Aborted)
                command.Status = CommandStatus.Aborted;

            if (command.Transaction.Status == TransactionStatus.Cleared)
                command.Status = CommandStatus.Executed;

            if (command.Transaction.Status == TransactionStatus.Error)
                command.Status = CommandStatus.Failed;
        }

        await _unitOfWork.PaymentRequestCommandRepository.UpdateRange(commands, CancellationToken.None);
        await _unitOfWork.PaymentTransactionRepository.UpdateRange(transactions.ToList()!, CancellationToken.None);
        await _unitOfWork.PaymentRequestRepository.Update(paymentRequest, CancellationToken.None);
        await _unitOfWork.SaveAsync(CancellationToken.None);
    }

    protected async Task<PaymentRequestViewModel?> CreatePaymentRequest(BasePaymentRequestMessage request, PaymentMethod paymentMethod = PaymentMethod.Ach)
    {
        var response = await _httpClient.ExecuteWithFullResultAsync<PaymentRequestViewModel>(HttpMethod.Post,
            EndpointPaths.CreatePaymentRequestPath,
            SerializePaymentRequestMessage(request));

        var paymentRequest = response.Content;

        if (paymentRequest.ConfirmationType != ConfirmationType.None && paymentRequest.ConfirmedAt is null)
            await ApprovePaymentRequest(paymentRequest.Id, paymentMethod);

        return response.Content;
    }

    protected static string SerializePaymentRequestMessage(BasePaymentRequestMessage request)
    {
        switch (request)
        {
            case InvoiceDisbursementV2RequestMessage invoiceDisbursementV2Request:
                return JsonSerializer.Serialize(invoiceDisbursementV2Request);
            case InvoicePaymentV2RequestMessage invoicePaymentV2Request:
                return JsonSerializer.Serialize(invoicePaymentV2Request);
            case InvoicePaymentRequestMessage payNowRequest:
                return JsonSerializer.Serialize(payNowRequest);
            case DrawRepaymentRequestMessage drawRepaymentRequest:
                return JsonSerializer.Serialize(drawRepaymentRequest);
            case FinalPaymentRequestMessage finalPaymentRequestMessage:
                return JsonSerializer.Serialize(finalPaymentRequestMessage);
            case FactoringDisbursementRequestMessage factoringDisbursementRequestMessage:
                return JsonSerializer.Serialize(factoringDisbursementRequestMessage);
            case FactoringFinalPaymentRequestMessage factoringFinalPaymentRequestMessage:
                return JsonSerializer.Serialize(factoringFinalPaymentRequestMessage);
            case DrawDisbursementRequestMessage drawDisbursementRequestMessage:
                return JsonSerializer.Serialize(drawDisbursementRequestMessage);
            case DrawRepaymentManualRequestMessage drawRepaymentManualRequestMessage:
                return JsonSerializer.Serialize(drawRepaymentManualRequestMessage);
            case SubscriptionFeePaymentRequestMessage subscriptionFeePaymentRequestMessage:
                return JsonSerializer.Serialize(subscriptionFeePaymentRequestMessage);
            default:
                throw new ArgumentException("Unsupported request type");
        }
    }

    protected async Task<PaymentRequestViewModel> CreateAndAssertPayment(BasePaymentRequestMessage message, bool skipDelays = true, PaymentMethod paymentMethod = PaymentMethod.Ach)
    {
        var firstStepNumber = 1;

        var paymentRequest = await CreatePaymentRequest(message, paymentMethod: paymentMethod);

        paymentRequest.ShouldNotBeNull();
        paymentRequest.Status.ShouldBe(PaymentRequestStatus.Requested);
        AssertTransactionStatuses(paymentRequest.Transactions, TransactionStatus.Placed, firstStepNumber);
        AssertCommandStatuses(paymentRequest.PaymentRequestCommands, CommandStatus.Pending, firstStepNumber);

        if (paymentRequest.RequestType is PaymentRequestType.DrawDisbursement or
            PaymentRequestType.FactoringDisbursement or
            PaymentRequestType.FinalPayment or
            PaymentRequestType.FinalPaymentV2 or
            PaymentRequestType.FactoringFinalPayment)
        {
            foreach (var transaction in paymentRequest.Transactions)
            {
                transaction.Amount.ShouldBe(paymentRequest.Amount);
            }
        }

        // Change created date to skip transfer delay
        /* TODO For now, it is not needed, but it may be useful in the future
        if (skipDelays && paymentRequest.RequestType is RequestType.DrawDisbursement or RequestType.FactoringDisbursement)
        {
            var transactions = await _dbContext!.PaymentTransactions!
                .Where(x => x.PaymentRequestId == paymentRequest.Id)
                .ToListAsync();

            foreach (var item in transactions)
            {
                item.CreatedAt = item.CreatedAt.AddDays(-2);
                item.Date = item.Date.AddDays(-2);
            }

            _dbContext.PaymentTransactions!.UpdateRange(transactions);
            await _dbContext.SaveChangesAsync();
        }*/

        return paymentRequest;
    }

    protected Task ExecutePaymentScheduledJob()
    {
        return _httpClient.PostAsync($"queueEvents/payment-scheduled-job", null);
    }

    protected async Task ExecuteTransactionStatusUpdateConsumer(TransactionStatusMessagePayload request)
    {
        var json = JsonSerializer.Serialize(request);
        var data = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync($"queueEvents/transaction-status-update-job", data);

        response.EnsureSuccessStatusCode();
    }

    protected async Task ExecuteTransactionStatusUpdateJob()
    {
        await _httpClient.GetAsync($"queueEvents/transaction-status-update-scheduled-job");
    }

    protected async Task ExecuteCommandManagement(Guid commandId)
    {
        var response = await _httpClient.GetAsync($"queueEvents/command-event-processor/{commandId}");
        response.EnsureSuccessStatusCode();
    }

    protected async Task ExecuteNotificationConsumer(NotificationMessagePayloadV2 request)
    {
        var json = JsonSerializer.Serialize(request);
        var data = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync($"queueEvents/notification-consumer", data);

        response.EnsureSuccessStatusCode();
    }

    protected async Task<PaginatedResultViewModel<PaymentRequestViewModel>?> GetPaymentRequests()
    {
        var response = await _httpClient.ExecuteWithFullResultAsync<PaginatedResultViewModel<PaymentRequestViewModel>>(HttpMethod.Get,
            "paymentRequests");

        return response.Content;
    }

    protected async Task<PaymentRequestViewModel> GetPaymentRequestById(Guid id)
    {
        var response = await _httpClient.ExecuteWithFullResultAsync<PaymentRequestViewModel>(HttpMethod.Get,
            $"paymentRequests/{id}");

        return response.Content;
    }

    protected async Task<List<PaymentRequestNotificationViewModel>> GetNotificationsByPaymentRequestId(Guid id)
    {
        var response = await _httpClient.ExecuteWithFullResultAsync<List<PaymentRequestNotificationViewModel>>(HttpMethod.Get,
            $"Notifications/payment-request/{id}");

        return response.Content;
    }

    protected async Task<PaymentWindowConfig> GetPaymentWindowConfig()
    {
        var response = await _httpClient.ExecuteWithFullResultAsync<PaymentWindowConfig>(HttpMethod.Get,
            "api/paymentwindow/config");

        return response.Content;
    }

    protected async Task UpdatePaymentWindowConfig(PaymentWindowConfig config)
    {
        var json = JsonSerializer.Serialize(config);
        var data = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClient.PutAsync("api/paymentwindow/config", data);

        response.EnsureSuccessStatusCode();
    }

    protected async Task ApprovePaymentRequest(Guid id, PaymentMethod paymentMethod = PaymentMethod.Ach)
    {
        // This is always return 415 as a response - found an information that it is some kind of bug it wabappfactory
        /*
        var request = new HttpRequestMessage(HttpMethod.Patch, $"paymentRequests/{id}/approve");
        request.Headers.Add("createdBy", "Integration test user");

        var approvalRequest = new PaymentApprovalRequest { PaymentMethod = PaymentMethod.Ach };
        var json = JsonSerializer.Serialize(approvalRequest);
        using var content = JsonContent.Create(approvalRequest);
        request.Content = content;

        var response = await _httpClient.SendAsync(request);

        response.EnsureSuccessStatusCode();
        */
        var paymentService = _serviceProvider.GetService<IPaymentRequestService>()!;
        await paymentService.ApprovePaymentRequest(id, "testUser", new PaymentApprovalRequest { PaymentMethod = paymentMethod },
            CancellationToken.None);
    }

    protected async Task<IEnumerable<PaymentRequestCommandViewModel>?> GetPaymentRequestCommands(string id)
    {
        var response = await _httpClient.ExecuteWithFullResultAsync<IEnumerable<PaymentRequestCommandViewModel>>(HttpMethod.Get,
            $"paymentRequests/{id}/commands");

        return response.Content;
    }

    protected async Task<IEnumerable<PaymentTransactionHistoryViewModel>?> GetTransactionHistory(string id)
    {
        var response = await _httpClient.ExecuteWithFullResultAsync<IEnumerable<PaymentTransactionHistoryViewModel>>(
            HttpMethod.Get,
            $"transactions/{id}/payment-transaction-history");

        return response.Content;
    }

    protected async Task ExecuteTransaction(string paymentRequestId, string transactionId, string updatedBy = "integrationTests")
    {
        var request = new HttpRequestMessage(HttpMethod.Post, $"paymentRequests/{paymentRequestId}/transactions/{transactionId}/execute");
        request.Headers.Add("updatedBy", updatedBy);
        var response = await _httpClient.SendAsync(request);

        response.EnsureSuccessStatusCode();
    }

    protected async Task RetryFailedTransaction(string paymentRequestId, string? transactionId, string userId = "integrationTestUser")
    {
        var request = new HttpRequestMessage(HttpMethod.Patch, $"paymentRequests/{paymentRequestId}/transactions/{transactionId}/retry");
        request.Headers.Add("userId", userId);
        var response = await _httpClient.SendAsync(request);

        response.EnsureSuccessStatusCode();
    }

    protected async Task MarkCommandAsExecuted(Guid commandId, string userId = "integrationTests")
    {
        var request = new HttpRequestMessage(HttpMethod.Put, $"transactions/command/executed/{commandId}");
        request.Headers.Add("userId", userId);
        var response = await _httpClient.SendAsync(request);

        response.EnsureSuccessStatusCode();
    }

    protected async Task<CompanyViewModel> AddCompanyToForbiddenList(string companyId)
    {
        ForbidCompanyRequestViewModel request = new(companyId, "test reason");

        var response = await _httpClient.ExecuteWithFullResultAsync<CompanyViewModel>(HttpMethod.Post,
            $"companies",
            JsonSerializer.Serialize(request),
            "userId",
            "testUser");

        return response.Content;
    }

    protected async Task<PaymentRequestViewModel?> PausePaymentRequest(Guid paymentRequestId, bool isPaused = true, string userId = "integrationTests")
    {
        PausePaymentRequestViewModel request = new(isPaused, PaymentPauseReason.Other, "test comment");

        var response = await _httpClient.ExecuteWithFullResultAsync<PaymentRequestViewModel>(HttpMethod.Put,
            $"paymentRequests/{paymentRequestId}/pause",
            JsonSerializer.Serialize(request),
            "userId",
            userId);

        return response.Content;
    }

    protected Task<PaymentRequestViewModel?> UnpausePaymentRequest(Guid paymentRequestId, string userId = "integrationTests")
    {
        return PausePaymentRequest(paymentRequestId, isPaused: false, userId: userId);
    }

    protected async Task MakeRecall(PaymentRequestViewModel details)
    {
        var transaction = details.Transactions.Single(x => x.SequenceNumber == 1);
        await ExecuteTransactionStatusUpdateConsumer(GetTransactionStatusMessage(transaction, AionStatuses.Error, "R06"));
        details = await GetPaymentRequestById(details.Id);
        details.Transactions.Single(x => x.SequenceNumber == 1).Status.ShouldBe(TransactionStatus.Recalled);
    }

    protected async Task ExecuteAndAssertTestCommandCycle(Guid paymentRequestId, int sequence, TransactionStatus finalStatus = TransactionStatus.Cleared, bool recalled = false)
    {
        var details = await GetPaymentRequestById(paymentRequestId);

        details.Status.ShouldBe(sequence == 1 ? PaymentRequestStatus.Requested : PaymentRequestStatus.Processing);

        await ExecutePaymentScheduledJob();
        details = await GetPaymentRequestById(paymentRequestId);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, sequence, recalled);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Pending, sequence, recalled);
        details.Status.ShouldBe(sequence == 1 ? PaymentRequestStatus.Requested : PaymentRequestStatus.Processing);

        await ReceiveAndExecuteCommandManagementMessages();

        details = await GetPaymentRequestById(paymentRequestId);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Processing, sequence, recalled);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Executing, sequence, recalled);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        var azureEntities = await MarkProcessingAzureTransactionsAsSent();

        await ExecuteTransactionStatusUpdateJob();
        await ReceiveAndExecuteTransactionMessages();

        details = await GetPaymentRequestById(paymentRequestId);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Processed, sequence, recalled);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Executing, sequence, recalled);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        switch (finalStatus)
        {
            case TransactionStatus.Error:
                await MarkProcessingAzureTransactionsAsError(azureEntities);

                await ExecuteTransactionStatusUpdateJob();
                await ReceiveAndExecuteTransactionMessages();

                details = await GetPaymentRequestById(paymentRequestId);
                AssertTransactionStatuses(details.Transactions, TransactionStatus.Failed, sequence, recalled);
                AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Failed, sequence, recalled);
                break;

            default:
                await MarkProcessingAzureTransactionsAsCleared(azureEntities);

                await ExecuteTransactionStatusUpdateJob();
                await ReceiveAndExecuteTransactionMessages();

                details = await GetPaymentRequestById(paymentRequestId);
                AssertTransactionStatuses(details.Transactions, TransactionStatus.Cleared, sequence, recalled);
                AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Executed, sequence, recalled);
                break;
        }
    }

    protected async Task<List<TransactionEntity>> MarkProcessingAzureTransactionsAsSent()
    {
        var paymentRequests = await GetPaymentRequests();
        var transactionsToUpdate =
            paymentRequests!.Result.SelectMany(x => x.Transactions)
                .Where(x => x.Status == TransactionStatus.Processing)
                .ToList();

        List<TransactionEntity> azureEntities = new();
        foreach (var transaction in transactionsToUpdate)
        {
            var entity = new TransactionEntity
            {
                //AionTransactionNumber = transaction1.ReferenceNumber,
                AionTransactionNumber = transaction.TransactionNumber,
                AionTransactionStatus = AionStatuses.Sent,
                IsProcessed = false,
                ErrorCode = "empty",
                StatusMessage = AionStatuses.Sent,
                BlueTapeTransactionNumber = transaction.TransactionNumber,
                BlueTapeTransactionId = transaction.Id.ToString(),
                AionTransactionType = AionTransactionType.EXTERNAL,
                AchId = Guid.NewGuid().ToString(),
                EffectiveDate = DateTime.Now,
                TransferType = AionTransferType.PULL
            };
            azureEntities.Add(entity);

            await _fakeAzureRepository.InsertOrReplaceEntityAsync(entity, TableUpdateMode.Merge, CancellationToken.None);
        }

        return azureEntities;
    }

    protected async Task<List<TransactionEntity>> MarkProcessingAzureTransactionsAsCleared(IEnumerable<TransactionEntity> azureEntities)
    {
        foreach (var entity in azureEntities)
        {
            // Make it cleared after 3 business days
            entity.EffectiveDate = entity.EffectiveDate!.Value.AddDays(-6);

            await _fakeAzureRepository.InsertOrReplaceEntityAsync(entity, TableUpdateMode.Merge, CancellationToken.None);
        }

        return azureEntities.ToList();
    }

    protected async Task<List<TransactionEntity>> MarkProcessingAzureTransactionsAsError(IEnumerable<TransactionEntity> azureEntities)
    {
        foreach (var entity in azureEntities)
        {
            // Make it cleared after 3 business days
            entity.AionTransactionStatus = AionStatuses.Error;
            entity.IsProcessed = false;

            await _fakeAzureRepository.InsertOrReplaceEntityAsync(entity, TableUpdateMode.Merge, CancellationToken.None);
        }

        return azureEntities.ToList();
    }

    protected async Task<List<Guid>> ReceiveAndExecuteCommandManagementMessages()
    {
        var commandIds = new List<Guid>();

        while (_fakePaymentFlowServiceMessageSender.TryDequeue(out var message))
        {
            await ExecuteCommandManagement(message.ExecutableCommandId);
            commandIds.Add(message.ExecutableCommandId);
        }

        return commandIds;
    }

    protected async Task ReceiveAndExecuteTransactionMessages()
    {
        while (_fakeTransactionStatusMessageSender.TryDequeue(out var message))
        {
            await ExecuteTransactionStatusUpdateConsumer(message);
        }
    }

    protected async Task ReceiveAndExecuteNotificationMessages()
    {
        while (_fakeNotificationMessageSender.TryDequeue(out var message))
        {
            await ExecuteNotificationConsumer(message);
        }
    }

    protected static void AssertTransactionStatuses(IEnumerable<PaymentTransactionViewModel> transactions, TransactionStatus assumedStatus, int sequence, bool recalled = false)
    {
        var processingTransaction = transactions.First(x => x.SequenceNumber == sequence);
        processingTransaction.Status.ShouldBe(assumedStatus, $"Current transaction with the status {processingTransaction.Status}");

        var unprocessedStepsStatus =
            assumedStatus is TransactionStatus.Error or TransactionStatus.Failed
                ? TransactionStatus.Aborted
                : TransactionStatus.Placed;

        foreach (var transaction in transactions.Where(x => x.SequenceNumber != sequence))
        {
            if (recalled && transaction.SequenceNumber == 1)
            {
                transaction.Status.ShouldBe(TransactionStatus.Recalled);
                continue;
            }
            // unprocessed
            if (transaction.SequenceNumber > sequence)
            {
                transaction.Status.ShouldBe(unprocessedStepsStatus);
                continue;
            }

            transaction.Status.ShouldBe(TransactionStatus.Cleared);
        }
    }

    protected static void AssertCommandStatuses(IEnumerable<PaymentRequestCommandViewModel> commands, CommandStatus assumedStatus, int sequence, bool recalled = false)
    {
        var processingCommand = commands.First(x => x.SequenceNumber == sequence);
        processingCommand.Status.ShouldBe(assumedStatus);

        var unprocessedStepsStatus =
            assumedStatus is CommandStatus.Failed
                ? CommandStatus.Aborted
                : CommandStatus.Placed;

        foreach (var command in commands.Where(x => x.SequenceNumber != sequence))
        {
            if (recalled && command.SequenceNumber == 1)
            {
                command.Status.ShouldBe(CommandStatus.Executed);
                continue;
            }

            command.Status.ShouldBe(command.SequenceNumber > sequence ? unprocessedStepsStatus : CommandStatus.Executed, $"Transaction step {command.SequenceNumber}, status  {command.Status}");
        }
    }

    protected static TransactionStatusMessagePayload GetTransactionStatusMessage(PaymentTransactionViewModel transaction, string status, string errorCode = "empty")
    {
        return new TransactionStatusMessagePayload()
        {
            BlueTapeTransactionNumber = transaction.TransactionNumber!,
            ExternalTransactionStatus = status,
            ExternalTransactionNumber = transaction.TransactionNumber!,
            StatusMessage = "message",
            ErrorCode = errorCode
        };
    }

    public void Dispose()
    {
        _dbContext?.Database.EnsureDeleted();
        _dbContext?.Dispose();
        _httpClient.Dispose();

        GC.SuppressFinalize(this);
    }
}
