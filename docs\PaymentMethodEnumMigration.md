# Payment Window Service - PaymentMethod Enum Migration

## Overview

The PaymentWindowService has been updated to use the `PaymentMethod` enum instead of string-based transaction types, providing better type safety and clearer semantics.

## Key Changes

### 1. PaymentMethod Enum Usage
Instead of using string arrays for transaction types, the service now uses the strongly-typed `PaymentMethod` enum:

```csharp
public enum PaymentMethod
{
    Card = 0,
    Ach = 1,
    TradeCredit = 2,
    SameDayAch = 3,
    Instant = 4,
    Wire = 5,
}
```

### 2. Updated Configuration Properties

**Before:**
```csharp
AllowedTransactionTypes = new List<string> { "AchPush", "AchPull", "WirePush" }
PausedTransactionTypes = new List<string> { "InstantPush" }
```

**After:**
```csharp
AllowedPaymentMethods = new List<PaymentMethod> { PaymentMethod.Ach, PaymentMethod.SameDayAch, PaymentMethod.Wire }
PausedPaymentMethods = new List<PaymentMethod> { PaymentMethod.Instant }
```

### 3. New API Methods

**PaymentMethod-Based Methods:**
```csharp
// Check if a payment method should be processed
bool shouldProcess = await paymentWindowService.ShouldProcessPaymentMethod(PaymentMethod.Ach, ct);

// Get currently allowed payment methods
List<PaymentMethod> allowedMethods = await paymentWindowService.GetCurrentAllowedPaymentMethods(ct);
```

**Legacy Transaction Type Support:**
```csharp
// Still supported for backward compatibility
bool shouldProcess = await paymentWindowService.ShouldProcessTransactionType(PaymentTransactionType.AchPush, ct);
```

### 4. Mapping Between PaymentTransactionType and PaymentMethod

The service automatically maps between transaction types and payment methods:

| PaymentTransactionType | PaymentMethod |
|------------------------|---------------|
| AchPush | Ach |
| AchInternal | Ach |
| AchPull | SameDayAch |
| CardPull | Card |
| InstantPush | Instant |
| WirePush | Wire |

## Configuration Examples

### Simple Default + Windows Configuration
```csharp
var config = new PaymentWindowConfig
{
    IsPaymentWindowEnabled = true,
    
    // Default configuration - always active
    DefaultConfig = new DefaultPaymentConfig
    {
        AllowedPaymentMethods = new List<PaymentMethod>
        {
            PaymentMethod.Ach,
            PaymentMethod.SameDayAch,
            PaymentMethod.Wire,
            PaymentMethod.Instant
        },
        PausedPaymentMethods = new List<PaymentMethod>(), // Nothing paused
        PaymentMethodPriorities = new Dictionary<PaymentMethod, int>
        {
            { PaymentMethod.Ach, 3 },
            { PaymentMethod.SameDayAch, 3 },
            { PaymentMethod.Wire, 5 },
            { PaymentMethod.Instant, 6 }
        }
    },
    
    // Specific payment windows
    PaymentWindows = new List<PaymentWindow>
    {
        new PaymentWindow
        {
            Id = "priority-ach",
            Name = "Priority ACH Processing",
            StartTime = new TimeSpan(9, 0, 0),
            DurationMinutes = 30,
            Priority = 1,
            AllowedPaymentMethods = new List<PaymentMethod> 
            { 
                PaymentMethod.Ach, PaymentMethod.SameDayAch 
            }
        }
    }
};
```

### Conservative Default with Liberal Windows
```csharp
// Conservative default - only ACH allowed
DefaultConfig = new DefaultPaymentConfig
{
    AllowedPaymentMethods = new List<PaymentMethod> { PaymentMethod.Ach },
    PausedPaymentMethods = new List<PaymentMethod> { PaymentMethod.Wire, PaymentMethod.Instant }
},

// Liberal business hours window
PaymentWindows = new List<PaymentWindow>
{
    new PaymentWindow
    {
        Id = "business-hours",
        StartTime = new TimeSpan(8, 0, 0),
        DurationMinutes = 480, // 8 hours
        AllowedPaymentMethods = new List<PaymentMethod> 
        { 
            PaymentMethod.Ach, PaymentMethod.SameDayAch, PaymentMethod.Wire, PaymentMethod.Instant
        }
    }
}
```

## Benefits

### 1. Type Safety
- Compile-time checking prevents typos and invalid payment method references
- IntelliSense support for better developer experience

### 2. Clearer Semantics
- Payment methods represent the actual payment mechanism rather than transaction direction
- Easier to understand and maintain business rules

### 3. Better Mapping
- Clear relationship between different transaction types and their underlying payment methods
- Simplified configuration for business users

### 4. Backward Compatibility
- Existing transaction type-based methods still work
- Gradual migration path available
- Legacy properties marked as obsolete with clear migration guidance

## Migration Path

### 1. Update Configuration Files
Replace string-based transaction types with PaymentMethod enum values:

```csharp
// Old
AllowedTransactionTypes = new List<string> { "AchPush", "WirePush" }

// New  
AllowedPaymentMethods = new List<PaymentMethod> { PaymentMethod.Ach, PaymentMethod.Wire }
```

### 2. Use New API Methods
Prefer PaymentMethod-based methods for new code:

```csharp
// New preferred approach
bool canProcessAch = await service.ShouldProcessPaymentMethod(PaymentMethod.Ach, ct);

// Legacy approach (still supported)
bool canProcessAchPush = await service.ShouldProcessTransactionType(PaymentTransactionType.AchPush, ct);
```

### 3. Update Business Logic
Use PaymentMethod enum in business rules and decision logic:

```csharp
if (allowedMethods.Contains(PaymentMethod.Instant))
{
    // Enable instant payment features
}
```

## Example Usage

```csharp
// Check current processing state
var processingMode = await service.GetCurrentProcessingMode(ct);
var allowedMethods = await service.GetCurrentAllowedPaymentMethods(ct);

Console.WriteLine($"Mode: {processingMode}");
Console.WriteLine($"Allowed: {string.Join(", ", allowedMethods)}");

// Check specific payment methods
var canProcessWire = await service.ShouldProcessPaymentMethod(PaymentMethod.Wire, ct);
var canProcessInstant = await service.ShouldProcessPaymentMethod(PaymentMethod.Instant, ct);

Console.WriteLine($"Wire allowed: {canProcessWire}");
Console.WriteLine($"Instant allowed: {canProcessInstant}");
```

This migration provides a more robust, type-safe foundation for payment processing configuration while maintaining full backward compatibility.
