using BlueTape.PaymentService.Application.Services;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlueTape.PaymentService.UnitTests.Services;

public class PaymentWindowServiceTests
{
    private readonly Mock<IPaymentConfigRepository> _paymentConfigRepositoryMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<ILogger<PaymentWindowService>> _loggerMock;
    private readonly PaymentWindowService _paymentWindowService;

    public PaymentWindowServiceTests()
    {
        _paymentConfigRepositoryMock = new Mock<IPaymentConfigRepository>();
        _dateProviderMock = new Mock<IDateProvider>();
        _loggerMock = new Mock<ILogger<PaymentWindowService>>();
        
        _paymentWindowService = new PaymentWindowService(
            _paymentConfigRepositoryMock.Object,
            _dateProviderMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task IsPaymentWindowActive_ShouldReturnTrue_WhenCurrentTimeIsWithinWindow()
    {
        // Arrange
        var currentTime = new DateTime(2024, 7, 16, 9, 15, 0, DateTimeKind.Utc); // Tuesday 9:15 AM
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentTime);
        
        var config = new PaymentWindowConfig
        {
            IsPaymentWindowEnabled = true,
            PaymentWindowStartTime = new TimeSpan(9, 0, 0), // 9:00 AM
            PaymentWindowDurationMinutes = 30,
            ActiveDays = new List<DayOfWeek> { DayOfWeek.Tuesday }
        };
        
        SetupConfigRepository(config);

        // Act
        var result = await _paymentWindowService.IsPaymentWindowActive(CancellationToken.None);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsPaymentWindowActive_ShouldReturnFalse_WhenCurrentTimeIsOutsideWindow()
    {
        // Arrange
        var currentTime = new DateTime(2024, 7, 16, 10, 0, 0, DateTimeKind.Utc); // Tuesday 10:00 AM
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentTime);
        
        var config = new PaymentWindowConfig
        {
            IsPaymentWindowEnabled = true,
            PaymentWindowStartTime = new TimeSpan(9, 0, 0), // 9:00 AM
            PaymentWindowDurationMinutes = 30, // Window ends at 9:30 AM
            ActiveDays = new List<DayOfWeek> { DayOfWeek.Tuesday }
        };
        
        SetupConfigRepository(config);

        // Act
        var result = await _paymentWindowService.IsPaymentWindowActive(CancellationToken.None);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsPaymentWindowActive_ShouldReturnFalse_WhenPaymentWindowIsDisabled()
    {
        // Arrange
        var currentTime = new DateTime(2024, 7, 16, 9, 15, 0, DateTimeKind.Utc); // Tuesday 9:15 AM
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentTime);
        
        var config = new PaymentWindowConfig
        {
            IsPaymentWindowEnabled = false, // Disabled
            PaymentWindowStartTime = new TimeSpan(9, 0, 0),
            PaymentWindowDurationMinutes = 30,
            ActiveDays = new List<DayOfWeek> { DayOfWeek.Tuesday }
        };
        
        SetupConfigRepository(config);

        // Act
        var result = await _paymentWindowService.IsPaymentWindowActive(CancellationToken.None);

        // Assert
        Assert.False(result);
    }

    [Theory]
    [InlineData(PaymentTransactionType.AchPush, true)]
    [InlineData(PaymentTransactionType.AchSameDayPush, true)]
    [InlineData(PaymentTransactionType.WirePush, false)]
    [InlineData(PaymentTransactionType.InstantPush, false)]
    public async Task ShouldProcessTransactionType_ShouldReturnCorrectResult_BasedOnTransactionType(
        PaymentTransactionType transactionType, bool expectedResult)
    {
        // Arrange
        var currentTime = new DateTime(2024, 7, 16, 9, 15, 0, DateTimeKind.Utc); // Tuesday 9:15 AM (within window)
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentTime);
        
        var config = new PaymentWindowConfig
        {
            IsPaymentWindowEnabled = true,
            PaymentWindowStartTime = new TimeSpan(9, 0, 0),
            PaymentWindowDurationMinutes = 30,
            ActiveDays = new List<DayOfWeek> { DayOfWeek.Tuesday }
        };
        
        SetupConfigRepository(config);

        // Act
        var result = await _paymentWindowService.ShouldProcessTransactionType(transactionType, CancellationToken.None);

        // Assert
        Assert.Equal(expectedResult, result);
    }

    [Fact]
    public async Task PrioritizeCommands_ShouldPrioritizeFinalPaymentsFirst()
    {
        // Arrange
        var commands = new List<PaymentRequestCommandEntity>
        {
            CreateCommand(PaymentRequestType.InvoicePayment, PaymentTransactionType.AchPush),
            CreateCommand(PaymentRequestType.FinalPayment, PaymentTransactionType.AchPush),
            CreateCommand(PaymentRequestType.DrawRepayment, PaymentTransactionType.AchSameDayPush)
        };

        var config = new PaymentWindowConfig();
        SetupConfigRepository(config);

        // Act
        var result = await _paymentWindowService.PrioritizeCommands(commands, CancellationToken.None);

        // Assert
        Assert.Equal(PaymentRequestType.FinalPayment, result.First().PaymentRequest!.RequestType);
    }

    [Fact]
    public async Task FilterCommandsByPaymentWindow_ShouldFilterOutPausedTransactions_DuringWindow()
    {
        // Arrange
        var currentTime = new DateTime(2024, 7, 16, 9, 15, 0, DateTimeKind.Utc); // Tuesday 9:15 AM (within window)
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentTime);
        
        var commands = new List<PaymentRequestCommandEntity>
        {
            CreateCommand(PaymentRequestType.InvoicePayment, PaymentTransactionType.AchPush),
            CreateCommand(PaymentRequestType.InvoicePayment, PaymentTransactionType.WirePush),
            CreateCommand(PaymentRequestType.InvoicePayment, PaymentTransactionType.InstantPush)
        };

        var config = new PaymentWindowConfig
        {
            IsPaymentWindowEnabled = true,
            PaymentWindowStartTime = new TimeSpan(9, 0, 0),
            PaymentWindowDurationMinutes = 30,
            ActiveDays = new List<DayOfWeek> { DayOfWeek.Tuesday }
        };
        
        SetupConfigRepository(config);

        // Act
        var result = await _paymentWindowService.FilterCommandsByPaymentWindow(commands, CancellationToken.None);

        // Assert
        Assert.Single(result); // Only ACH should remain
        Assert.Equal(PaymentTransactionType.AchPush, result.First().Transaction!.TransactionType);
    }

    private void SetupConfigRepository(PaymentWindowConfig config)
    {
        var configEntity = new PaymentConfigEntity
        {
            ConfigKey = "PAYMENT_WINDOW_CONFIG",
            ConfigValue = System.Text.Json.JsonSerializer.Serialize(config)
        };
        
        _paymentConfigRepositoryMock
            .Setup(x => x.GetByConfigKey("PAYMENT_WINDOW_CONFIG", It.IsAny<CancellationToken>()))
            .ReturnsAsync(configEntity);
    }

    private static PaymentRequestCommandEntity CreateCommand(PaymentRequestType requestType, PaymentTransactionType transactionType)
    {
        return new PaymentRequestCommandEntity
        {
            Id = Guid.NewGuid(),
            PaymentRequest = new PaymentRequestEntity
            {
                Id = Guid.NewGuid(),
                RequestType = requestType
            },
            Transaction = new PaymentTransactionEntity
            {
                Id = Guid.NewGuid(),
                TransactionType = transactionType,
                Amount = 100m
            }
        };
    }
}
