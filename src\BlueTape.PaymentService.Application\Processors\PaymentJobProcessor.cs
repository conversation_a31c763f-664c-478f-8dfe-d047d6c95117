using BlueTape.CompanyClient.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Abstractions.Processors;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External;
using BlueTape.PaymentService.Application.Constants;
using BlueTape.PaymentService.Application.Mappers;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Extensions;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.Domain.Models;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using IPaymentRequestCommandRepository = BlueTape.PaymentService.DataAccess.Abstractions.Repositories.IPaymentRequestCommandRepository;

namespace BlueTape.PaymentService.Application.Processors;

public class PaymentJobProcessor(IPaymentRequestCommandRepository paymentRequestCommandRepository,
    IUnitOfWork unitOfWork,
    IPaymentFlowTemplatesEngine paymentFlowTemplatesEngine,
    IPaymentFlowServiceMessageSender messageSender,
    IFailedCommandsProcessor failedCommandsProcessor,
    ICompanyHttpClient companyHttpClient,
    IAionServiceV2 aionService,
    IPaymentConfigService paymentConfigService,
    IPaymentWindowService paymentWindowService,
    IDateProvider dateProvider,
    ILogger<PaymentJobProcessor> logger) : IPaymentJobProcessor
{
    public async Task Process(CancellationToken ct)
    {
        await CheckAndResetAionDailyLimits(ct);

        var getResult = await GetPrioritizedCommandsAndGroupings(ct);

        // Get all commands grouped by PaymentRequestId that could be executed
        var commandsGroupedByPaymentRequestId = getResult.commandsGroupedByPaymentRequestId;
        var commandsToExecute = getResult.commandsToExecute;

        // Get commands that are in Executing state and have a transaction type of AchInternal
        var processingInternalTransferCommands = commandsGroupedByPaymentRequestId
            .SelectMany(group => group)
            .Where(command => command.Status == CommandStatus.Executing &&
                              command.Transaction?.TransactionType == PaymentTransactionType.AchInternal)
            .ToList();

        var groupsWithFailedCommands = FindGroupsWithFailedCommands(commandsGroupedByPaymentRequestId);

        await failedCommandsProcessor.ProcessFailedCommands(groupsWithFailedCommands, ct);

        ////////
        // Pause for payment requests if it retrined from Aion and has not enough balance history in the last 3 hours
        var histories = await unitOfWork.GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(commandsToExecute.Select(x => x.TransactionId), ct);
        var recentHistoriesTransactionIds = histories.Where(x => x != null && (DateTime.UtcNow - x.CreatedAt).TotalHours < 3)
            .Select(x => x?.TransactionId)
            .ToList();

        commandsToExecute = recentHistoriesTransactionIds is null
            ? commandsToExecute
            : commandsToExecute.Where(x => !recentHistoriesTransactionIds.Contains(x.TransactionId)).ToList();
        ///////

        // Filter commands by payment window rules (ACH/Same Day ACH during window, pause instant/wire during window)
        commandsToExecute = await paymentWindowService.FilterCommandsByPaymentWindow(commandsToExecute, ct);

        // Filter out commands that are blocked by daily limits
        commandsToExecute = await FilterCommandsByDailyLimits(commandsToExecute, ct);

        // Exclude executable commands by balance checking
        await FilterCommandsByEligibleBalance(commandsToExecute, processingInternalTransferCommands, ct);

        await UpdateStatusesBeforeExecution(commandsToExecute, ct);

        var messages = commandsToExecute.Select(x =>
            new ServiceBusMessageBt<PaymentJobMessagePayload>(new PaymentJobMessagePayload { ExecutableCommandId = x.Id }))
            .ToArray();

        if (messages.Length != 0)
        {
            await messageSender.SendMessages(messages, ct);
        }

        // Log payment window status for monitoring
    }

    public async Task<(List<IGrouping<Guid, PaymentRequestCommandEntity>> commandsGroupedByPaymentRequestId,
        List<PaymentRequestCommandEntity> commandsToExecute)> GetPrioritizedCommandsAndGroupings(CancellationToken ct)
    {
        var commandsGroupedByPaymentRequestId = await paymentRequestCommandRepository.GetItemsGroupedByPaymentRequestId(ct);

        // Get list of commands that need to be executed by all basic conditions
        var commandsToExecute = await GetCommandsToExecute(commandsGroupedByPaymentRequestId, ct);

        // Prioritize commands based on payment window rules (final payments first, then push transfers, then internal)
        commandsToExecute = PrioritizeCommands(commandsToExecute, ct);

        return (commandsGroupedByPaymentRequestId, commandsToExecute);
    }

    public async Task<List<PaymentRequestCommandEntity>> GetCommandsToExecute(CancellationToken ct)
    {
        var result = await GetPrioritizedCommandsAndGroupings(ct);

        return result.commandsToExecute;
    }

    private async Task<List<PaymentRequestCommandEntity>> GetCommandsToExecute(List<IGrouping<Guid, PaymentRequestCommandEntity>> commandsGroupedByPaymentRequestId, CancellationToken ct)
    {
        var commandsToExecute = new List<PaymentRequestCommandEntity>();
        commandsGroupedByPaymentRequestId.ForEach(group =>
        {
            var groupCommandsToExecute = GetGroupCommandsToExecute(group);
            commandsToExecute.AddRange(groupCommandsToExecute);
        });

        var merchantIds = commandsToExecute
            .Where(x => x.StepName == StepName.PullFromMerchant.ToString())
            .Select(x => x.PaymentRequest!.PayeeId)
            .ToArray();

        if (merchantIds.Length != 0)
        {
            var companies = (await companyHttpClient.GetCompaniesByIdsAsync(merchantIds!, ct));

            commandsToExecute = commandsToExecute.Where(x =>
                    x.StepName != StepName.PullFromMerchant.ToString() ||
                    (x.StepName == StepName.PullFromMerchant.ToString() && companies.Exists(y => y.MerchantAutomaticPullAllowed)))
                .ToList();
        }

        return commandsToExecute;
    }

    private List<PaymentRequestCommandEntity> GetGroupCommandsToExecute(IGrouping<Guid, PaymentRequestCommandEntity> group)
    {
        var commands = group.Select(x => x)
            .OrderBy(x => x.Transaction?.SequenceNumber)
            .ToList();

        if (commands.Count == 0 || IsAnyCommandInGroupAlreadyExecuting(commands))
            return new List<PaymentRequestCommandEntity>();

        var commandsToExecute =
            commands.Where(command => IsCommandExecutable(command, commands)).ToList();

        return commandsToExecute;
    }

    private static bool IsAnyCommandInGroupAlreadyExecuting(List<PaymentRequestCommandEntity> transactions)
    {
        return transactions.Exists(x => x.Status is CommandStatus.Executing);
    }

    private bool IsCommandExecutable(PaymentRequestCommandEntity commandToCheck, IEnumerable<PaymentRequestCommandEntity> commands)
    {
        var otherCommandsInGroup = commands.Where(x => x.Id != commandToCheck.Id && x.Status != CommandStatus.Retried);
        return commandToCheck.Status switch
        {
            CommandStatus.Executed => false,
            CommandStatus.Placed or CommandStatus.Pending =>
                paymentFlowTemplatesEngine.IsCommandSatisfiesExecutionConditions(commandToCheck, otherCommandsInGroup),
            _ => false
        };
    }

    private async Task UpdateStatusesBeforeExecution(List<PaymentRequestCommandEntity> commandsToExecute, CancellationToken ct)
    {
        var notPendingCommandsToExecute = commandsToExecute.Where(x => x.Status is not CommandStatus.Pending).ToList();
        notPendingCommandsToExecute.ForEach(x =>
        {
            x.Status = CommandStatus.Pending;
            x.UpdatedBy = ApplicationConstants.PaymentJob;
        });

        if (notPendingCommandsToExecute.Count != 0)
        {
            await unitOfWork.PaymentRequestCommandRepository.UpdateRange(commandsToExecute, ct);
            await unitOfWork.SaveAsync(ct);
        }
    }

    private static IEnumerable<IGrouping<Guid, PaymentRequestCommandEntity>> FindGroupsWithFailedCommands(IEnumerable<IGrouping<Guid, PaymentRequestCommandEntity>> commandsGroupedByPaymentRequestId)
    {
        return commandsGroupedByPaymentRequestId.Where(x => x.Select(command => command)
                                                            .Any(command => command.Status is CommandStatus.Failed
                                                            && command.Transaction!.Status != TransactionStatus.Error));
    }

    private async Task FilterCommandsByEligibleBalance(List<PaymentRequestCommandEntity> commandsToExecute, List<PaymentRequestCommandEntity> processingInternalTransfers, CancellationToken ct)
    {
        if (commandsToExecute.Count == 0)
            return;

        //var stepsToCheckBalance = paymentFlowTemplatesEngine.GetStepNamesWithWaitForEligibleBalanceCondition();
        var transactionsToCheckBalance = new List<PaymentTransactionType>
            { PaymentTransactionType.InstantPush, PaymentTransactionType.WirePush, PaymentTransactionType.AchPush };

        var paymentTypesToCheckBalance = new List<PaymentRequestType>
        {
            PaymentRequestType.FactoringDisbursement,
            PaymentRequestType.FactoringFinalPayment,
            PaymentRequestType.DrawDisbursement,
            PaymentRequestType.FinalPayment,
            PaymentRequestType.FinalPaymentV2,
        };

        var commandsToCheckBalance = commandsToExecute
            //.Where(x => stepsToCheckBalance.Contains(x.StepName))
            .Where(x => paymentTypesToCheckBalance.Contains(x.PaymentRequest.RequestType) && transactionsToCheckBalance.Contains(x.Transaction.TransactionType))
            .GroupBy(x => x.Transaction!.ReceiverAccountId) //GroupBy BlueTape account that will used for disbursement
            .ToList();

        foreach (var group in commandsToCheckBalance)
        {
            var paymentRequestId = group.First().PaymentRequestId;
            var receiverAccountId = group.Key;
            var receiverAccount = receiverAccountId.MapAccountCodeToEnum();
            var subscription = AccountCodeMapper.MapAccountTypeToSubscription(receiverAccount);

            var balance = await aionService.GetAccountBalance(receiverAccount, paymentRequestId, subscription, ct);
            var commandsInGroup = group.ToList();

            var prioritizedPaymentsFromAccount =
                commandsToExecute.Where(x => x.Transaction!.ReceiverAccountId == receiverAccountId && !commandsInGroup.Contains(x))
                    .ToList();

            // Freeze funds for processing internal transfers
            var incomingInstantTransfers =
                processingInternalTransfers.Where(x => x.Transaction.OriginatorAccountId == receiverAccountId);

            balance -= prioritizedPaymentsFromAccount.Sum(x => x.Transaction!.Amount);
            balance -= incomingInstantTransfers.Sum(x => x.Transaction!.Amount);

            for (var i = 0; i < commandsInGroup.Count; i++)
            {
                var command = commandsInGroup[i];

                if (balance >= command.Transaction!.Amount)
                {
                    balance -= command.Transaction.Amount;
                }
                else
                {
                    ExcludeNonEligibleCommands(commandsToExecute, commandsInGroup.Skip(i).ToList());
                    break;
                }
            }
        }
    }

    private static void ExcludeNonEligibleCommands(List<PaymentRequestCommandEntity> allPayments, List<PaymentRequestCommandEntity> excludedCommands)
    {
        foreach (var payment in excludedCommands)
        {
            allPayments.Remove(payment);
        }
    }
    private async Task<List<PaymentRequestCommandEntity>> FilterCommandsByDailyLimits(List<PaymentRequestCommandEntity> commands, CancellationToken ct)
    {
        var filteredCommands = new List<PaymentRequestCommandEntity>();
        var allDailyLimitConfigs = await paymentConfigService.GetAllAionDailyLimitConfigs(ct);

        foreach (var command in commands)
        {
            if (command.Transaction?.TransactionType == null || command.PaymentRequest?.PaymentSubscription == null)
            {
                filteredCommands.Add(command);
                continue;
            }

            var isLimitExceeded = IsAionDailyLimitExceeded(
                command.Transaction.TransactionType,
                command.PaymentRequest.PaymentSubscription,
                allDailyLimitConfigs);

            if (!isLimitExceeded)
            {
                filteredCommands.Add(command);
            }
        }

        return filteredCommands;
    }

    private bool IsAionDailyLimitExceeded(
        PaymentTransactionType transactionType,
        PaymentSubscriptionType subscriptionType,
        Dictionary<PaymentSubscriptionType, AionDailyLimitConfig> allDailyLimitConfigs)
    {
        if (!allDailyLimitConfigs.TryGetValue(subscriptionType, out var dailyLimitConfig))
            return false;

        return transactionType switch
        {
            PaymentTransactionType.WirePush => dailyLimitConfig.WireLimitExceeded,
            PaymentTransactionType.InstantPush => dailyLimitConfig.InstantLimitExceeded,
            PaymentTransactionType.AchPull => dailyLimitConfig.AchPullLimitExceeded,
            PaymentTransactionType.AchPush => dailyLimitConfig.AchPushLimitExceeded,
            _ => false
        };
    }

    private async Task CheckAndResetAionDailyLimits(CancellationToken ct)
    {
        var currentTime = dateProvider.CurrentDateTime;

        if (currentTime.Hour == 0 && currentTime.IsBusinessDay())
        {
            await paymentConfigService.ResetAllAionDailyLimits(ct);
        }
    }

    public List<PaymentRequestCommandEntity> PrioritizeCommands(List<PaymentRequestCommandEntity> commands, CancellationToken ct)
    {
        var paymentMethodPriority = new Dictionary<PaymentMethod, int>
        {
            { PaymentMethod.Instant, 3 },
            { PaymentMethod.Wire, 3 },
            { PaymentMethod.SameDayAch, 4 },
            { PaymentMethod.Ach, 5 },
        };

        // This implementation should process already processing payments as well
        var prioritizedCommands = commands.OrderBy(x => !paymentWindowService.IsPrefundedFinalPayment(x))
            .ThenBy(x => paymentMethodPriority.TryGetValue(x.PaymentRequest.PaymentMethod, out var order) ? order : int.MaxValue)
            .ThenBy(x => x.PaymentRequest.ConfirmedAt)
            .ThenBy(x => x.PaymentRequest.CreatedAt)
            .ToList();

        logger.LogDebug("Commands prioritized: Final payments: {FinalCount}, Total: {TotalCount}",
            prioritizedCommands.Count(paymentWindowService.IsPrefundedFinalPayment), prioritizedCommands.Count);

        return prioritizedCommands;
    }
}
