using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Models;

namespace BlueTape.PaymentService.Application.Abstractions.Services;

public interface IPaymentWindowService
{
    Task<bool> IsPaymentWindowActive(CancellationToken ct);

    List<PaymentWindow> GetActivePaymentWindows(PaymentWindowConfig config);

    Task<PaymentWindowConfig> GetPaymentWindowConfig(CancellationToken ct);

    Task UpdatePaymentWindowConfig(PaymentWindowConfig config, string updatedBy, CancellationToken ct);

    bool ShouldProcessPaymentMethod(PaymentWindowConfig config, PaymentRequestCommandEntity command, CancellationToken ct);

    bool IsPrefundedFinalPayment(PaymentRequestCommandEntity command);

    Task<List<PaymentRequestCommandEntity>> FilterCommandsByPaymentWindow(List<PaymentRequestCommandEntity> commands, CancellationToken ct);
}
