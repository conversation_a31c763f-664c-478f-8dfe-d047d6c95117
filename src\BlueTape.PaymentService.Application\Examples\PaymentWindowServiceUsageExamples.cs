using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Domain.Models;

namespace BlueTape.PaymentService.Application.Examples;

/// <summary>
/// Examples demonstrating how to use the enhanced PaymentWindowService with multiple payment windows
/// </summary>
public class PaymentWindowServiceUsageExamples
{
    private readonly IPaymentWindowService _paymentWindowService;

    public PaymentWindowServiceUsageExamples(IPaymentWindowService paymentWindowService)
    {
        _paymentWindowService = paymentWindowService;
    }

    /// <summary>
    /// Example: Setting up multiple payment windows
    /// </summary>
    public async Task SetupMultiplePaymentWindows(CancellationToken ct)
    {
        // Configure two payment windows - morning and afternoon
        var config = PaymentWindowConfigExamples.TwoWindowConfiguration;
        
        await _paymentWindowService.UpdatePaymentWindowConfig(config, "SystemAdmin", ct);
        
        Console.WriteLine("Multiple payment windows configured successfully:");
        Console.WriteLine($"- Morning Window: {config.PaymentWindows[0].StartTime} for {config.PaymentWindows[0].DurationMinutes} minutes");
        Console.WriteLine($"- Afternoon Window: {config.PaymentWindows[1].StartTime} for {config.PaymentWindows[1].DurationMinutes} minutes");
    }

    /// <summary>
    /// Example: Checking which payment windows are currently active
    /// </summary>
    public async Task CheckActiveWindows(CancellationToken ct)
    {
        // Check if any payment window is active
        var isAnyActive = await _paymentWindowService.IsPaymentWindowActive(ct);
        Console.WriteLine($"Any payment window active: {isAnyActive}");

        // Get all active windows
        var activeWindows = await _paymentWindowService.GetActivePaymentWindows(ct);
        Console.WriteLine($"Number of active windows: {activeWindows.Count}");
        
        foreach (var window in activeWindows)
        {
            Console.WriteLine($"- Active window: {window.Name} (Priority: {window.Priority})");
        }

        // Get the highest priority active window
        var primaryWindow = await _paymentWindowService.GetActivePaymentWindow(ct);
        if (primaryWindow != null)
        {
            Console.WriteLine($"Primary active window: {primaryWindow.Name}");
        }
    }

    /// <summary>
    /// Example: Setting up specialized windows for different business needs
    /// </summary>
    public async Task SetupSpecializedWindows(CancellationToken ct)
    {
        var config = new PaymentWindowConfig
        {
            IsPaymentWindowEnabled = true,
            PaymentWindows = new List<PaymentWindow>
            {
                // High-priority window for urgent ACH payments
                new PaymentWindow
                {
                    Id = "urgent-ach",
                    Name = "Urgent ACH Processing",
                    StartTime = new TimeSpan(8, 0, 0), // 8:00 AM
                    DurationMinutes = 30,
                    Priority = 1,
                    ActiveDays = new List<DayOfWeek> 
                    { 
                        DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, 
                        DayOfWeek.Thursday, DayOfWeek.Friday 
                    },
                    AllowedTransactionTypes = new List<string> { "AchPush" }
                },
                
                // Standard window for all ACH types
                new PaymentWindow
                {
                    Id = "standard-ach",
                    Name = "Standard ACH Processing", 
                    StartTime = new TimeSpan(14, 0, 0), // 2:00 PM
                    DurationMinutes = 60,
                    Priority = 2,
                    ActiveDays = new List<DayOfWeek> 
                    { 
                        DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, 
                        DayOfWeek.Thursday, DayOfWeek.Friday 
                    },
                    AllowedTransactionTypes = new List<string> { "AchPush", "AchPull", "AchInternal" }
                },
                
                // Weekend window for internal transfers only
                new PaymentWindow
                {
                    Id = "weekend-internal",
                    Name = "Weekend Internal Transfers",
                    StartTime = new TimeSpan(10, 0, 0), // 10:00 AM
                    DurationMinutes = 120,
                    Priority = 3,
                    ActiveDays = new List<DayOfWeek> { DayOfWeek.Saturday, DayOfWeek.Sunday },
                    AllowedTransactionTypes = new List<string> { "AchInternal" }
                }
            },
            PausedTransactionTypes = new List<string> { "InstantPush", "WirePush" },
            FinalPaymentPriority = 0,
            AchPushPriority = 1,
            AchInternalPriority = 2
        };

        await _paymentWindowService.UpdatePaymentWindowConfig(config, "BusinessAdmin", ct);
        Console.WriteLine("Specialized payment windows configured:");
        Console.WriteLine("- Urgent ACH: 8:00 AM (30 min) - AchPush only");
        Console.WriteLine("- Standard ACH: 2:00 PM (60 min) - All ACH types");
        Console.WriteLine("- Weekend Internal: 10:00 AM (120 min) - Internal transfers only");
    }

    /// <summary>
    /// Example: Dynamic window management based on business rules
    /// </summary>
    public async Task DynamicWindowManagement(CancellationToken ct)
    {
        var config = await _paymentWindowService.GetPaymentWindowConfig(ct);
        
        // Add a temporary high-priority window for end-of-month processing
        var isEndOfMonth = DateTime.Now.Day >= 28;
        if (isEndOfMonth)
        {
            var urgentWindow = new PaymentWindow
            {
                Id = "end-of-month-urgent",
                Name = "End of Month Urgent Processing",
                StartTime = new TimeSpan(7, 0, 0), // 7:00 AM
                DurationMinutes = 120,
                Priority = 0, // Highest priority
                ActiveDays = new List<DayOfWeek> 
                { 
                    DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, 
                    DayOfWeek.Thursday, DayOfWeek.Friday 
                },
                AllowedTransactionTypes = new List<string> { "AchPush", "AchPull", "AchInternal" }
            };
            
            // Insert at the beginning for highest priority
            config.PaymentWindows.Insert(0, urgentWindow);
            
            await _paymentWindowService.UpdatePaymentWindowConfig(config, "AutomatedSystem", ct);
            Console.WriteLine("End-of-month urgent processing window added");
        }
    }

    /// <summary>
    /// Example: Getting next window information for scheduling
    /// </summary>
    public async Task GetNextWindowInfo(CancellationToken ct)
    {
        var nextWindowStart = await _paymentWindowService.GetNextPaymentWindowStartTime(ct);
        if (nextWindowStart.HasValue)
        {
            Console.WriteLine($"Next payment window starts at: {nextWindowStart.Value:yyyy-MM-dd HH:mm:ss}");
            
            var timeUntilNext = nextWindowStart.Value - DateTime.UtcNow;
            Console.WriteLine($"Time until next window: {timeUntilNext.TotalMinutes:F0} minutes");
        }
        else
        {
            Console.WriteLine("No upcoming payment windows found");
        }

        var currentWindowEnd = await _paymentWindowService.GetCurrentPaymentWindowEndTime(ct);
        if (currentWindowEnd.HasValue)
        {
            var timeRemaining = currentWindowEnd.Value - DateTime.UtcNow;
            Console.WriteLine($"Current window ends in: {timeRemaining.TotalMinutes:F0} minutes");
        }
    }
}
