{"Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.DataProtection": "Error", "Microsoft.EntityFrameworkCore": "Warning", "System.Net.Http.HttpClient": "Warning", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Warning"}}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=paymentdb-test;Username=********;Password=********"}, "NET-LMS-API-URL": "https://lms.bluetape.com", "NET-LMS-APIKEY": "12345678", "PaymentWindowConfig": {"IsPaymentWindowEnabled": false, "AllowedPaymentSubscriptions": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "AffectedPaymentTypes": ["FactoringDisbursement", "FactoringFinalPayment", "DrawDisbursement", "FinalPayment", "FinalPaymentV2"], "DefaultConfig": {"AllowedPaymentMethods": ["Wire", "Instant"], "PaymentMethodPriorities": {"Ach": 5, "SameDayAch": 6, "Wire": 3, "Instant": 3, "Card": 7}}, "PaymentWindows": [{"Id": "priority-ach", "Name": "Same Day ACH Window", "StartTime": "09:23:00", "DurationMinutes": 10, "Priority": 1, "IsEnabled": true, "ActiveDays": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"], "AllowedPaymentMethods": ["SameDayAch"], "AllowedPaymentTypes": ["FactoringDisbursement", "DrawDisbursement", "FinalPayment"]}, {"Id": "priority-ach-prefunded", "Name": "Same Day ACH Window", "StartTime": "09:33:00", "DurationMinutes": 30, "Priority": 2, "IsEnabled": true, "ActiveDays": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"], "AllowedPaymentMethods": ["SameDayAch"], "AllowedPaymentTypes": ["FactoringFinalPayment", "FinalPaymentV2"]}, {"Id": "standard-ach", "Name": "Standard ACH Window", "StartTime": "15:23:00", "DurationMinutes": 10, "Priority": 1, "IsEnabled": true, "ActiveDays": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"], "AllowedPaymentMethods": ["Ach"], "AllowedPaymentTypes": ["FactoringDisbursement", "DrawDisbursement", "FinalPayment"]}, {"Id": "standard-ach-prefunded", "Name": "Standard ACH Window", "StartTime": "15:33:00", "DurationMinutes": 30, "Priority": 2, "IsEnabled": true, "ActiveDays": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"], "AllowedPaymentMethods": ["Ach"], "AllowedPaymentTypes": ["FactoringFinalPayment", "FinalPaymentV2"]}]}}